// 会话管理模块
import { computed } from 'vue'
import type { Ref } from 'vue'
import { apiClient } from '../../api'
import type { ChatSession, Message } from './types'

export class SessionManager {
  private chatSessions: Ref<Map<string, ChatSession>>
  private currentChatUserId: Ref<string>
  private messages: Ref<Map<string, Message[]>>

  constructor(
    chatSessions: Ref<Map<string, ChatSession>>,
    currentChatUserId: Ref<string>,
    messages: Ref<Map<string, Message[]>>
  ) {
    this.chatSessions = chatSessions
    this.currentChatUserId = currentChatUserId
    this.messages = messages
  }

  // 计算属性：按时间排序的聊天会话
  get sortedChatSessions() {
    return computed(() => {
      return Array.from(this.chatSessions.value.values()).sort(
        (a, b) => b.lastActiveTime - a.lastActiveTime
      )
    })
  }

  // 计算属性：总未读消息数
  get totalUnreadCount() {
    return computed(() => {
      return Array.from(this.chatSessions.value.values()).reduce(
        (total, session) => total + session.unreadCount,
        0
      )
    })
  }

  // 更新聊天会话
  async updateChatSession(userId: string, lastMessage: Message, getCurrentUserId: () => string) {
    console.log(`开始更新聊天会话 - 用户ID: ${userId}, 消息:`, lastMessage)
    let session = this.chatSessions.value.get(userId)

    if (!session) {
      // 创建新的聊天会话，需要获取用户信息
      try {
        const userDetail = await apiClient.getUserDetail(userId)
        if (userDetail.success) {
          session = {
            userId,
            userName: userDetail.user.displayName,
            userAvatar: userDetail.user.avatar,
            lastMessage,
            unreadCount: 0,
            lastActiveTime: lastMessage.timestamp
          }
        } else {
          // 如果获取用户信息失败，使用默认信息
          session = {
            userId,
            userName: `用户${userId}`,
            userAvatar: '/avatars/default.png',
            lastMessage,
            unreadCount: 0,
            lastActiveTime: lastMessage.timestamp
          }
        }
      } catch (err) {
        console.error('获取用户信息失败:', err)
        session = {
          userId,
          userName: `用户${userId}`,
          userAvatar: '/avatars/default.png',
          lastMessage,
          unreadCount: 0,
          lastActiveTime: lastMessage.timestamp
        }
      }
    } else {
      // 更新现有会话
      session.lastMessage = lastMessage
      session.lastActiveTime = lastMessage.timestamp
    }

    // 如果不是当前聊天用户且消息不是自己发送的，增加未读计数
    const currentUserId = getCurrentUserId()
    const isCurrentChatUser = userId === this.currentChatUserId.value
    const isOwnMessage = lastMessage.senderId === currentUserId
    if (!isCurrentChatUser && !isOwnMessage) {
      session.unreadCount++
    }

    this.chatSessions.value.set(userId, session)
  }

  // 设置当前聊天用户
  setCurrentChatUser(userId: string, getCurrentUserId: () => string) {
    this.currentChatUserId.value = userId

    // 清除该用户的未读计数
    const session = this.chatSessions.value.get(userId)
    if (session) {
      session.unreadCount = 0
      this.chatSessions.value.set(userId, session)
    }

    // 标记该用户的所有消息为已读
    const userMessages = this.messages.value.get(userId)
    if (userMessages) {
      userMessages.forEach((msg) => {
        if (msg.senderId !== getCurrentUserId()) {
          msg.isRead = true
        }
      })
    }
  }

  // 批量设置聊天会话（用于从API加载的联系人数据）
  setChatSessions(sessions: ChatSession[]) {
    sessions.forEach((session) => {
      this.chatSessions.value.set(session.userId, session)
    })
  }

  // 创建空的聊天会话（用于没有消息历史的用户）
  async createEmptySession(userId: string) {
    try {
      const userDetail = await apiClient.getUserDetail(userId)
      if (userDetail.success) {
        const emptySession: ChatSession = {
          userId,
          userName: userDetail.user.displayName,
          userAvatar: userDetail.user.avatar,
          lastMessage: undefined,
          unreadCount: 0,
          lastActiveTime: Date.now()
        }
        this.chatSessions.value.set(userId, emptySession)
        return emptySession
      } else {
        console.error('获取用户信息失败:', userDetail.message)
        return null
      }
    } catch (err) {
      console.error('创建空聊天会话失败:', err)
      return null
    }
  }

  // 获取会话信息
  getSession(userId: string): ChatSession | undefined {
    return this.chatSessions.value.get(userId)
  }

  // 检查是否存在会话
  hasSession(userId: string): boolean {
    return this.chatSessions.value.has(userId)
  }

  // 删除会话
  removeSession(userId: string) {
    this.chatSessions.value.delete(userId)
  }

  // 清空所有会话
  clearAllSessions() {
    this.chatSessions.value.clear()
  }

  // 获取会话数量
  getSessionCount(): number {
    return this.chatSessions.value.size
  }
}
